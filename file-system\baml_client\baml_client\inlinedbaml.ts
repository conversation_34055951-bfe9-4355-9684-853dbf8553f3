/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

const fileMap = {
  
  "agent.baml": "function DetermineNextStep(\n    thread: string \n) -> FileSystemTools | HumanTools {\n    client QwenClient\n\n    prompt #\"\n        {{ _.role(\"system\") }}\n\n        You are a helpful file system assistant that can help with reading, writing, and modifying files.\n\n        Available tools:\n        - read_file: Read content from a file (supports offset and limit for large files)\n        - write_file: Write content to a file (creates directories if needed)\n        - replace: Replace specific text within a file (requires exact context)\n        - done_for_now: Indicate completion with a message\n        - request_clarification: Ask for clarification from the user\n\n        Important guidelines:\n        - Accept both absolute and relative paths (relative paths will be resolved from the working directory)\n        - For replace operations, include at least 3 lines of context before and after the target text\n        - Match whitespace and indentation precisely in replace operations\n        - Be careful with file modifications - they are permanent\n        - If a user provides a simple filename like \"example.txt\", treat it as a relative path\n\n        {{ _.role(\"user\") }}\n\n        You are working on the following thread:\n\n        {{ thread }}\n\n        What should the next step be?\n\n        {{ ctx.output_format }}\n\n        Think step by step about what action to take:\n        1. Analyze the user's request\n        2. Determine if you need to read files first to understand the context\n        3. Choose the appropriate tool\n        4. Provide the necessary parameters\n\n        {...} // schema\n    \"#\n}\n\ntest ReadFileTest {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n        Can you read the content of /path/to/file.txt?\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"read_file\"}})\n  @@assert(path, {{this.path == \"/path/to/file.txt\"}})\n}\n\ntest WriteFileTest {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n        Create a new file at /path/to/new.txt with content \"Hello World\"\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"write_file\"}})\n  @@assert(file_path, {{this.file_path == \"/path/to/new.txt\"}})\n}\n\ntest ReplaceTest {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      <user_input>\n        In /path/to/file.txt, replace the following text:\n\n        function oldFunction() {\n            return \"old text\";\n        }\n\n        with:\n\n        function newFunction() {\n            return \"new text\";\n        }\n      </user_input>\n    \"#\n  }\n  @@assert(intent, {{this.intent == \"replace\"}})\n}\n",
  "clients.baml": "client<llm> QwenClient {\n  provider openai\n  options {\n    model \"qwen2.5-32b-instruct-int4\"\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\n    api_key env.QWEN_API_KEY\n  }\n}\n",
  "file_tools.baml": "// File system tool definitions\n\nclass ReadFileTool {\n  intent \"read_file\" @description(\"Read content from a file\")\n  path string @description(\"Absolute path to the file to read\")\n  offset int? @description(\"Optional: 0-based line number to start reading from\")\n  limit int? @description(\"Optional: maximum number of lines to read\")\n}\n\nclass WriteFileTool {\n  intent \"write_file\" @description(\"Write content to a file\")\n  file_path string @description(\"Absolute path to the file to write to\")\n  content string @description(\"Content to write into the file\")\n}\n\nclass ReplaceTool {\n  intent \"replace\" @description(\"Replace text within a file\")\n  file_path string @description(\"Absolute path to the file to modify\")\n  old_string string @description(\"Exact literal text to replace (must include context)\")\n  new_string string @description(\"Exact literal text to replace old_string with\")\n  expected_replacements int? @description(\"Number of occurrences to replace (defaults to 1)\")\n}\n\nclass DoneForNow {\n  intent \"done_for_now\"\n  message string @description(\"Message to send to the user about the work that was done\")\n}\n\nclass RequestClarification {\n  intent \"request_clarification\"\n  message string @description(\"Request clarification from the user\")\n}\n\ntype FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool\ntype HumanTools = DoneForNow | RequestClarification\n",
  "generators.baml": "generator lang_typescript {\n  output_type typescript\n  output_dir \"../baml_client\"\n  version \"0.85.0\"\n}\n",
}
export const getBamlFiles = () => {
    return fileMap;
}