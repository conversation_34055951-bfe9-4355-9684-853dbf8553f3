/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { Image, Audio, Pdf, Video } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type {  DoneForNow,  ReadFileTool,  ReplaceTool,  RequestClarification,  WriteFileTool } from "./types"
import type * as types from "./types"

/******************************************************************************
*
*  These types are used for streaming, for when an instance of a type
*  is still being built up and any of its fields is not yet fully available.
*
******************************************************************************/

export interface StreamState<T> {
  value: T
  state: "Pending" | "Incomplete" | "Complete"
}

export namespace partial_types {
    export interface DoneForNow {
      intent?: "done_for_now" | null
      message?: string | null
    }
    export interface ReadFileTool {
      intent?: "read_file" | null
      path?: string | null
      offset?: number | null
      limit?: number | null
    }
    export interface ReplaceTool {
      intent?: "replace" | null
      file_path?: string | null
      old_string?: string | null
      new_string?: string | null
      expected_replacements?: number | null
    }
    export interface RequestClarification {
      intent?: "request_clarification" | null
      message?: string | null
    }
    export interface WriteFileTool {
      intent?: "write_file" | null
      file_path?: string | null
      content?: string | null
    }
export type FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool | null

export type HumanTools = DoneForNow | RequestClarification | null

}