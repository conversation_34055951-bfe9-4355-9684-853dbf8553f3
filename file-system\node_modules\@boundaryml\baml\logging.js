"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setLogMaxChunkLength = exports.setLogJsonMode = exports.getLogLevel = exports.setLogLevel = void 0;
var native_1 = require("./native");
Object.defineProperty(exports, "setLogLevel", { enumerable: true, get: function () { return native_1.setLogLevel; } });
Object.defineProperty(exports, "getLogLevel", { enumerable: true, get: function () { return native_1.getLogLevel; } });
Object.defineProperty(exports, "setLogJsonMode", { enumerable: true, get: function () { return native_1.setLogJsonMode; } });
Object.defineProperty(exports, "setLogMaxChunkLength", { enumerable: true, get: function () { return native_1.setLogMaxChunkLength; } });
