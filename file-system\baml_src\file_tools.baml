// File system tool definitions

class ReadFileTool {
  intent "read_file" @description("Read content from a file")
  path string @description("Absolute path to the file to read")
  offset int? @description("Optional: 0-based line number to start reading from")
  limit int? @description("Optional: maximum number of lines to read")
}

class WriteFileTool {
  intent "write_file" @description("Write content to a file")
  file_path string @description("Absolute path to the file to write to")
  content string @description("Content to write into the file")
}

class ReplaceTool {
  intent "replace" @description("Replace text within a file")
  file_path string @description("Absolute path to the file to modify")
  old_string string @description("Exact literal text to replace (must include context)")
  new_string string @description("Exact literal text to replace old_string with")
  expected_replacements int? @description("Number of occurrences to replace (defaults to 1)")
}

class DoneForNow {
  intent "done_for_now"
  message string @description("Message to send to the user about the work that was done")
}

class RequestClarification {
  intent "request_clarification"
  message string @description("Request clarification from the user")
}

type FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool
type HumanTools = DoneForNow | RequestClarification
