/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { BamlRuntime, BamlCtxManager, ClientRegistry, Image, Audio, Pdf, Video } from "@boundaryml/baml"
import { toBamlError, HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type * as types from "./types"
import type {DoneForNow, ReadFileTool, ReplaceTool, RequestClarification, WriteFileTool} from "./types"
import type TypeBuilder from "./type_builder"

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  env?: Record<string, string | undefined>
}

export class HttpRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  DetermineNextStep(
      thread: string,
      __baml_options__?: BamlCallOptions
  ): HTTPRequest {
    try {
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      return this.runtime.buildRequestSync(
        "DetermineNextStep",
        {
          "thread": thread
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env,
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export class HttpStreamRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  DetermineNextStep(
      thread: string,
      __baml_options__?: BamlCallOptions
  ): HTTPRequest {
    try {
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      return this.runtime.buildRequestSync(
        "DetermineNextStep",
        {
          "thread": thread
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env,
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}
