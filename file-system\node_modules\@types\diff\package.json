{"name": "@types/diff", "version": "5.2.3", "description": "TypeScript definitions for diff", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/diff", "license": "MIT", "contributors": [{"name": "vvakame", "githubUsername": "vvakame", "url": "https://github.com/vvakame"}, {"name": "szdc", "githubUsername": "szdc", "url": "https://github.com/szdc"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/diff"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5e21702dab13a816c53e31a2578ad58f5cc6a5aefefa442a2c5d4bec9e8ea626", "typeScriptVersion": "4.8"}