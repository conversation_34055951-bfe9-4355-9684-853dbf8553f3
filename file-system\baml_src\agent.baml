function DetermineNextStep(
    thread: string 
) -> FileSystemTools | HumanTools {
    client QwenClient

    prompt #"
        {{ _.role("system") }}

        You are a helpful file system assistant that can help with reading, writing, and modifying files.

        Available tools:
        - read_file: Read content from a file (supports offset and limit for large files)
        - write_file: Write content to a file (creates directories if needed)
        - replace: Replace specific text within a file (requires exact context)
        - done_for_now: Indicate completion with a message
        - request_clarification: Ask for clarification from the user

        Important guidelines:
        - Accept both absolute and relative paths (relative paths will be resolved from the working directory)
        - For replace operations, include at least 3 lines of context before and after the target text
        - Match whitespace and indentation precisely in replace operations
        - Be careful with file modifications - they are permanent
        - If a user provides a simple filename like "example.txt", treat it as a relative path

        {{ _.role("user") }}

        You are working on the following thread:

        {{ thread }}

        What should the next step be?

        {{ ctx.output_format }}

        Think step by step about what action to take:
        1. Analyze the user's request
        2. Determine if you need to read files first to understand the context
        3. Choose the appropriate tool
        4. Provide the necessary parameters

        {...} // schema
    "#
}

test ReadFileTest {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
        Can you read the content of /path/to/file.txt?
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "read_file"}})
  @@assert(path, {{this.path == "/path/to/file.txt"}})
}

test WriteFileTest {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
        Create a new file at /path/to/new.txt with content "Hello World"
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "write_file"}})
  @@assert(file_path, {{this.file_path == "/path/to/new.txt"}})
}

test ReplaceTest {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
        In /path/to/file.txt, replace the following text:

        function oldFunction() {
            return "old text";
        }

        with:

        function newFunction() {
            return "new text";
        }
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "replace"}})
}
