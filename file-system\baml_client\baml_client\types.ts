/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { Image, Audio, Pdf, Video } from "@boundaryml/baml"
/**
 * Recursively partial type that can be null.
 *
 * @deprecated Use types from the `partial_types` namespace instead, which provides type-safe partial implementations
 * @template T The type to make recursively partial.
 */
export type RecursivePartialNull<T> = T extends object
    ? { [P in keyof T]?: RecursivePartialNull<T[P]> }
    : T | null;

export interface Checked<T,CheckName extends string = string> {
    value: T,
    checks: Record<CheckName, Check>,
}

export interface Check {
    name: string,
    expr: string
    status: "succeeded" | "failed"
}

export function all_succeeded<CheckName extends string>(checks: Record<CheckName, Check>): boolean {
    return get_checks(checks).every(check => check.status === "succeeded")
}

export function get_checks<CheckName extends string>(checks: Record<CheckName, Check>): Check[] {
    return Object.values(checks)
}
export interface DoneForNow {
  intent: "done_for_now"
  message: string
  
}

export interface ReadFileTool {
  intent: "read_file"
  path: string
  offset?: number | null
  limit?: number | null
  
}

export interface ReplaceTool {
  intent: "replace"
  file_path: string
  old_string: string
  new_string: string
  expected_replacements?: number | null
  
}

export interface RequestClarification {
  intent: "request_clarification"
  message: string
  
}

export interface WriteFileTool {
  intent: "write_file"
  file_path: string
  content: string
  
}

export type FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool

export type HumanTools = DoneForNow | RequestClarification
